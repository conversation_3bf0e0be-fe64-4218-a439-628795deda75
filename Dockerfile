FROM python:3.11-slim

WORKDIR /app

# System deps
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential curl ca-certificates && \
    rm -rf /var/lib/apt/lists/*

# Install Python deps once (rebuild only when requirements.txt changes)
COPY requirements.txt /app/requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Data dir for SQLite
RUN mkdir -p /data
VOLUME ["/data"]

# Runtime env
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
# Default to Postgres; can be overridden by env/.env
ENV DB_URL=postgresql+psycopg://vocab_user:vocab_pass@db:5432/vocab_db

# App code is mounted via docker-compose for hot-reload-style dev
CMD ["python", "-m", "app.bot"]
