# Copy to .env and fill values

# Telegram
TELEGRAM_BOT_TOKEN=123456789:PUT-YOUR-TOKEN-HERE
DEFAULT_TARGET_LANG=ru
ADMIN_USER_IDS=

# Postgres container credentials (used by the db service)
POSTGRES_DB=vocab_db
POSTGRES_USER=vocab_user
POSTGRES_PASSWORD=vocab_pass

# App DB connection string (uses the db service hostname "db")
# If your password has special characters, URL-encode them.
DB_URL=postgresql+psycopg://vocab_user:vocab_pass@db:5432/vocab_db