# api/tg_webhook_v4.py - Minimal serverless-safe version
import os
import logging
import asyncio
from starlette.applications import Starlette
from starlette.requests import Request
from starlette.responses import <PERSON><PERSON><PERSON>esponse, PlainTextResponse
from telegram import Update, Bot
from telegram.ext import Application as PTBApp, ContextTypes
from telegram.ext import <PERSON><PERSON><PERSON><PERSON>, MessageHandler, CallbackQueryHandler, filters

from app.config import TELEGRAM_BOT_TOKEN
from app.db import Base, engine

# ---- Logging ----
logging.basicConfig(
    level=logging.INFO,
    format='{"level":"%(levelname)s","msg":"%(message)s","ts":"%(asctime)s"}'
)
logger = logging.getLogger("tg_webhook_v4")

# Import handlers but create wrapper versions that don't rely on context.bot
from app.bot import (
    start as _start, help_cmd as _help_cmd, setlang as _setlang, 
    add_cmd as _add_cmd, import_cmd as _import_cmd,
    today as _today, week as _week, month as _month, 
    review_cmd as _review_cmd, reset_cmd as _reset_cmd, 
    set_counter_cmd as _set_counter_cmd, stats as _stats, 
    remove_cmd as _remove_cmd, on_button as _on_button, 
    on_text as _on_text
)

# Global variables
_ptb_app = None
_bot_instance = None
_init_lock = asyncio.Lock()

class ServerlessContext:
    """Minimal context that provides bot instance."""
    def __init__(self, bot):
        self.bot = bot
        self.args = []

async def create_serverless_handlers(bot):
    """Create handler wrappers that work in serverless environment."""
    
    async def start_wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE):
        # Replace context.bot calls with direct bot calls if needed
        return await _start(update, context)
    
    async def help_wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE):
        return await _help_cmd(update, context)
    
    async def setlang_wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE):
        return await _setlang(update, context)
    
    async def add_wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE):
        return await _add_cmd(update, context)
    
    async def import_wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE):
        return await _import_cmd(update, context)
    
    async def today_wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE):
        return await _today(update, context)
    
    async def week_wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE):
        return await _week(update, context)
    
    async def month_wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE):
        return await _month(update, context)
    
    async def review_wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE):
        return await _review_cmd(update, context)
    
    async def reset_wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE):
        return await _reset_cmd(update, context)
    
    async def set_counter_wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE):
        return await _set_counter_cmd(update, context)
    
    async def remove_wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE):
        return await _remove_cmd(update, context)
    
    async def stats_wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE):
        return await _stats(update, context)
    
    async def button_wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE):
        return await _on_button(update, context)
    
    async def text_wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE):
        return await _on_text(update, context)
    
    return {
        'start': start_wrapper,
        'help': help_wrapper,
        'setlang': setlang_wrapper,
        'add': add_wrapper,
        'import': import_wrapper,
        'today': today_wrapper,
        'week': week_wrapper,
        'month': month_wrapper,
        'review': review_wrapper,
        'reset': reset_wrapper,
        'set_counter': set_counter_wrapper,
        'remove': remove_wrapper,
        'stats': stats_wrapper,
        'button': button_wrapper,
        'text': text_wrapper,
    }

async def get_ptb_app():
    """Get or create PTB application with minimal configuration."""
    global _ptb_app, _bot_instance
    
    if _ptb_app is not None:
        return _ptb_app, _bot_instance
    
    async with _init_lock:
        if _ptb_app is not None:
            return _ptb_app, _bot_instance
        
        logger.info("Creating minimal PTB application")
        
        # Ensure tables exist
        Base.metadata.create_all(bind=engine)
        
        # Create bot instance
        _bot_instance = Bot(token=TELEGRAM_BOT_TOKEN)
        
        # Create PTB application with minimal settings
        app = PTBApp.builder().token(TELEGRAM_BOT_TOKEN).build()
        
        # Get handler wrappers
        handlers = await create_serverless_handlers(_bot_instance)
        
        # Register handlers
        app.add_handler(CommandHandler("start", handlers['start']))
        app.add_handler(CommandHandler("help", handlers['help']))
        app.add_handler(CommandHandler("setlang", handlers['setlang']))
        app.add_handler(CommandHandler("add", handlers['add']))
        app.add_handler(CommandHandler("import", handlers['import']))
        app.add_handler(CommandHandler("today", handlers['today']))
        app.add_handler(CommandHandler("week", handlers['week']))
        app.add_handler(CommandHandler("month", handlers['month']))
        app.add_handler(CommandHandler("review", handlers['review']))
        app.add_handler(CommandHandler("reset", handlers['reset']))
        app.add_handler(CommandHandler("set_counter", handlers['set_counter']))
        app.add_handler(CommandHandler("remove", handlers['remove']))
        app.add_handler(CommandHandler("stats", handlers['stats']))
        app.add_handler(CallbackQueryHandler(handlers['button']))
        app.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handlers['text']))
        
        # Initialize without starting (to avoid event loop issues)
        await app.initialize()
        
        _ptb_app = app
        logger.info("Minimal PTB application created successfully")
        return _ptb_app, _bot_instance

# Create Starlette app
app = Starlette()
SECRET = os.environ.get("TELEGRAM_WEBHOOK_SECRET", "")

@app.route("/api/tg_webhook_v4", methods=["GET"])
async def health(req: Request):
    logger.info('health_check_v4 method=%s path=%s', req.method, req.url.path)
    try:
        ptb_app, bot = await get_ptb_app()
        return PlainTextResponse("ok - v4 minimal")
    except Exception as e:
        logger.exception("health_check_v4_error: %s", e)
        return PlainTextResponse("error", status_code=500)

@app.route("/api/tg_webhook_v4", methods=["POST"])
async def webhook(req: Request):
    try:
        ptb_app, bot = await get_ptb_app()
        
        if SECRET:
            incoming = req.headers.get("x-telegram-bot-api-secret-token")
            ok = (incoming == SECRET)
            logger.info('secret_check ok=%s has_header=%s', ok, bool(incoming))
            if not ok:
                return PlainTextResponse("forbidden", status_code=403)

        data = await req.json()
        logger.info('update_received keys=%s', list(data.keys()))

        # Create update object
        update = Update.de_json(data, bot)
        
        # Process update with minimal approach
        try:
            # Create a simple context
            context = ServerlessContext(bot)
            
            # Process the update manually to avoid event loop issues
            for handler in ptb_app.handlers[0]:  # Get handlers from first group
                if handler.check_update(update):
                    await handler.handle_update(update, ptb_app, None, context)
                    break
            
            logger.info('update_processed ok=true')
        except Exception as process_error:
            logger.exception("Error processing update: %s", process_error)
            
        return JSONResponse({"ok": True})
        
    except Exception as e:
        logger.exception("webhook_error: %s", e)
        return JSONResponse({"ok": False}, status_code=200)

# Catch-all GET for debugging
@app.route("/{rest_of_path:path}", methods=["GET"])
async def health_catchall(req: Request):
    current_file = os.path.abspath(__file__)
    logger.info('health_catchall_v4 method=%s path=%s file=%s', req.method, req.url.path, current_file)
    return PlainTextResponse(f"ok (v4 backup)")

# CRITICAL: Expose the ASGI app for Vercel's Python runtime
application = app
