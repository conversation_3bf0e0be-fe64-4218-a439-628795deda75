# api/tg_webhook_v2.py - Alternative serverless-friendly approach
import os
import logging
import asyncio
from starlette.applications import Starlette
from starlette.requests import Request
from starlette.responses import J<PERSON><PERSON>esponse, PlainTextResponse
from telegram import Update, BotCommand
from telegram.ext import Application as PTBApp
from telegram.ext import <PERSON><PERSON><PERSON><PERSON>, MessageHandler, CallbackQueryHandler, filters

from app.config import TELEGRAM_BOT_TOKEN
from app.db import Base, engine
from app.bot import (
    start, help_cmd, setlang, add_cmd, import_cmd,
    today, week, month, review_cmd,
    reset_cmd, set_counter_cmd, stats, remove_cmd,
    on_button, on_text
)

# ---- Logging (captured by Vercel) ----
logging.basicConfig(
    level=logging.INFO,
    format='{"level":"%(levelname)s","msg":"%(message)s","ts":"%(asctime)s"}'
)
logger = logging.getLogger("tg_webhook")

# Global variables for lazy initialization
_ptb_app = None
_init_lock = asyncio.Lock()

async def get_ptb_app():
    """Get or create the PTB application with proper initialization."""
    global _ptb_app

    if _ptb_app is not None:
        return _ptb_app

    async with _init_lock:
        if _ptb_app is not None:  # Double-check after acquiring lock
            return _ptb_app

        logger.info("Creating and initializing PTB application")

        # Ensure tables exist
        Base.metadata.create_all(bind=engine)

        # Create PTB application with serverless-friendly configuration
        app = (PTBApp.builder()
               .token(TELEGRAM_BOT_TOKEN)
               .get_updates_read_timeout(30)
               .get_updates_write_timeout(30)
               .get_updates_connect_timeout(30)
               .get_updates_pool_timeout(30)
               .build())

        # Register handlers
        app.add_handler(CommandHandler("start", start))
        app.add_handler(CommandHandler("help", help_cmd))
        app.add_handler(CommandHandler("setlang", setlang))
        app.add_handler(CommandHandler("add", add_cmd))
        app.add_handler(CommandHandler("import", import_cmd))
        app.add_handler(CommandHandler("today", today))
        app.add_handler(CommandHandler("week", week))
        app.add_handler(CommandHandler("month", month))
        app.add_handler(CommandHandler("review", review_cmd))
        app.add_handler(CommandHandler("reset", reset_cmd))
        app.add_handler(CommandHandler("set_counter", set_counter_cmd))
        app.add_handler(CommandHandler("remove", remove_cmd))
        app.add_handler(CommandHandler("stats", stats))
        app.add_handler(CallbackQueryHandler(on_button))
        app.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, on_text))

        # Initialize the application
        await app.initialize()
        await app.start()

        # Set bot commands (skip if it fails to avoid blocking)
        try:
            await app.bot.set_my_commands([
                BotCommand("start", "Start the bot"),
                BotCommand("help", "Show help"),
                BotCommand("setlang", "Set target language (e.g., /setlang ru)"),
                BotCommand("add", "Add a word or phrase"),
                BotCommand("import", "Bulk import newline-separated words"),
                BotCommand("today", "List today's words"),
                BotCommand("week", "List this week's words"),
                BotCommand("month", "List this month's words"),
                BotCommand("review", "Start review (today|week|month)"),
                BotCommand("reset", "Reset a word's counter to 0"),
                BotCommand("set_counter", "Set global mastery threshold"),
                BotCommand("remove", "Remove a word from your list"),
                BotCommand("stats", "Show your stats"),
            ])
        except Exception as e:
            logger.warning("set_my_commands failed: %s", e)

        _ptb_app = app
        logger.info("PTB application initialized successfully")
        return _ptb_app

# Create Starlette app without startup hooks
app = Starlette()

SECRET = os.environ.get("TELEGRAM_WEBHOOK_SECRET", "")

@app.route("/api/tg_webhook_v2", methods=["GET"])
async def health(req: Request):
    logger.info('health_check_v2 method=%s path=%s', req.method, req.url.path)
    try:
        ptb = await get_ptb_app()
        return PlainTextResponse("ok - v2 initialized")
    except Exception as e:
        logger.exception("health_check_v2_error: %s", e)
        return PlainTextResponse("error", status_code=500)

@app.route("/api/tg_webhook_v2", methods=["POST"])
async def webhook(req: Request):
    try:
        # Get initialized PTB app
        ptb = await get_ptb_app()
        
        if SECRET:
            incoming = req.headers.get("x-telegram-bot-api-secret-token")
            ok = (incoming == SECRET)
            logger.info('secret_check ok=%s has_header=%s', ok, bool(incoming))
            if not ok:
                return PlainTextResponse("forbidden", status_code=403)

        data = await req.json()
        logger.info('update_received keys=%s', list(data.keys()))

        update = Update.de_json(data, ptb.bot)

        # Process update with error handling for event loop issues
        try:
            await ptb.process_update(update)
            logger.info('update_processed ok=true')
        except RuntimeError as re:
            if "Event loop is closed" in str(re):
                logger.warning("Event loop closed error - this is expected in serverless")
                # The update was likely processed successfully despite the error
            else:
                logger.exception("Runtime error processing update: %s", re)
        except Exception as process_error:
            logger.exception("Error processing update: %s", process_error)

        return JSONResponse({"ok": True})
    except Exception as e:
        logger.exception("webhook_error: %s", e)
        return JSONResponse({"ok": False}, status_code=200)

# Catch-all GET for debugging
@app.route("/{rest_of_path:path}", methods=["GET"])
async def health_catchall(req: Request):
    current_file = os.path.abspath(__file__)
    logger.info('health_catchall method=%s path=%s file=%s', req.method, req.url.path, current_file)
    return PlainTextResponse(f"ok (backup)")

# CRITICAL: Expose the ASGI app for Vercel's Python runtime
application = app
