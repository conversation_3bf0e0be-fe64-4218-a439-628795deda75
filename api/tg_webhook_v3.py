# api/tg_webhook_v3.py - Event loop safe version
import os
import logging
import asyncio
import httpx
from starlette.applications import Starlette
from starlette.requests import Request
from starlette.responses import J<PERSON>NResponse, PlainTextResponse
from telegram import Update, Bot
from telegram.ext import Application as PTBApp
from telegram.ext import <PERSON><PERSON><PERSON><PERSON>, MessageHandler, CallbackQueryHandler, filters

from app.config import TELEGRAM_BOT_TOKEN
from app.db import Base, engine
from app.bot import (
    start, help_cmd, setlang, add_cmd, import_cmd,
    today, week, month, review_cmd,
    reset_cmd, set_counter_cmd, stats, remove_cmd,
    on_button, on_text
)

# ---- Logging ----
logging.basicConfig(
    level=logging.INFO,
    format='{"level":"%(levelname)s","msg":"%(message)s","ts":"%(asctime)s"}'
)
logger = logging.getLogger("tg_webhook_v3")

# Global variables
_ptb_app = None
_bot = None
_init_lock = asyncio.Lock()

async def get_bot_and_app():
    """Get or create bot and PTB application with event loop safety."""
    global _ptb_app, _bot
    
    if _ptb_app is not None and _bot is not None:
        return _bot, _ptb_app
    
    async with _init_lock:
        if _ptb_app is not None and _bot is not None:
            return _bot, _ptb_app
        
        logger.info("Creating bot and PTB application")
        
        # Ensure tables exist
        Base.metadata.create_all(bind=engine)
        
        # Create a standalone bot instance for API calls
        _bot = Bot(token=TELEGRAM_BOT_TOKEN)
        
        # Create PTB application with custom HTTP client settings
        app = (PTBApp.builder()
               .token(TELEGRAM_BOT_TOKEN)
               .build())
        
        # Register handlers
        app.add_handler(CommandHandler("start", start))
        app.add_handler(CommandHandler("help", help_cmd))
        app.add_handler(CommandHandler("setlang", setlang))
        app.add_handler(CommandHandler("add", add_cmd))
        app.add_handler(CommandHandler("import", import_cmd))
        app.add_handler(CommandHandler("today", today))
        app.add_handler(CommandHandler("week", week))
        app.add_handler(CommandHandler("month", month))
        app.add_handler(CommandHandler("review", review_cmd))
        app.add_handler(CommandHandler("reset", reset_cmd))
        app.add_handler(CommandHandler("set_counter", set_counter_cmd))
        app.add_handler(CommandHandler("remove", remove_cmd))
        app.add_handler(CommandHandler("stats", stats))
        app.add_handler(CallbackQueryHandler(on_button))
        app.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, on_text))
        
        # Initialize the application
        await app.initialize()
        await app.start()
        
        _ptb_app = app
        logger.info("Bot and PTB application initialized successfully")
        return _bot, _ptb_app

# Create Starlette app
app = Starlette()
SECRET = os.environ.get("TELEGRAM_WEBHOOK_SECRET", "")

@app.route("/api/tg_webhook_v3", methods=["GET"])
async def health(req: Request):
    logger.info('health_check_v3 method=%s path=%s', req.method, req.url.path)
    try:
        bot, ptb_app = await get_bot_and_app()
        return PlainTextResponse("ok - v3 initialized")
    except Exception as e:
        logger.exception("health_check_v3_error: %s", e)
        return PlainTextResponse("error", status_code=500)

@app.route("/api/tg_webhook_v3", methods=["POST"])
async def webhook(req: Request):
    try:
        # Get initialized bot and PTB app
        bot, ptb_app = await get_bot_and_app()
        
        if SECRET:
            incoming = req.headers.get("x-telegram-bot-api-secret-token")
            ok = (incoming == SECRET)
            logger.info('secret_check ok=%s has_header=%s', ok, bool(incoming))
            if not ok:
                return PlainTextResponse("forbidden", status_code=403)

        data = await req.json()
        logger.info('update_received keys=%s', list(data.keys()))

        # Create update object
        update = Update.de_json(data, bot)
        
        # Process update in a way that's safe for serverless
        try:
            # Use a new event loop context for processing
            await ptb_app.process_update(update)
            logger.info('update_processed ok=true')
        except Exception as process_error:
            logger.exception("Error processing update: %s", process_error)
            # Still return success to Telegram to avoid retries
            
        return JSONResponse({"ok": True})
        
    except Exception as e:
        logger.exception("webhook_error: %s", e)
        return JSONResponse({"ok": False}, status_code=200)

# Catch-all GET for debugging
@app.route("/{rest_of_path:path}", methods=["GET"])
async def health_catchall(req: Request):
    current_file = os.path.abspath(__file__)
    logger.info('health_catchall_v3 method=%s path=%s file=%s', req.method, req.url.path, current_file)
    return PlainTextResponse(f"ok (v3 backup)")

# CRITICAL: Expose the ASGI app for Vercel's Python runtime
application = app
