# api/telegram.py
import os
from starlette.applications import Starlette
from starlette.requests import Request
from starlette.responses import JSONResponse, PlainTextResponse
from telegram import Update
from telegram.ext import Application as PTBApp
from telegram.ext import (
    <PERSON><PERSON><PERSON><PERSON>, Message<PERSON><PERSON><PERSON>, CallbackQueryHandler, filters
)

# import your existing bot pieces
from app.config import TELEGRAM_BOT_TOKEN
from app.db import Base, engine
from app.bot import (
    start, help_cmd, setlang, add_cmd, import_cmd,
    today, week, month, review_cmd,
    reset_cmd, set_counter_cmd, stats,
    on_button, on_text
)

# Build the PTB application once (module scope) so it reuses across warm starts
def build_ptb():
    Base.metadata.create_all(bind=engine)
    ptb = PTBApp.builder().token(TELEGRAM_BOT_TOKEN).build()
    # register the same handlers you use in polling mode
    ptb.add_handler(CommandHandler("start", start))
    ptb.add_handler(CommandHandler("help", help_cmd))
    ptb.add_handler(CommandHandler("setlang", setlang))
    ptb.add_handler(CommandHandler("add", add_cmd))
    ptb.add_handler(CommandHandler("import", import_cmd))
    ptb.add_handler(CommandHandler("today", today))
    ptb.add_handler(CommandHandler("week", week))
    ptb.add_handler(CommandHandler("month", month))
    ptb.add_handler(CommandHandler("review", review_cmd))
    ptb.add_handler(CommandHandler("reset", reset_cmd))
    ptb.add_handler(CommandHandler("set_counter", set_counter_cmd))
    ptb.add_handler(CommandHandler("stats", stats))
    ptb.add_handler(CallbackQueryHandler(on_button))
    ptb.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, on_text))
    return ptb

ptb = build_ptb()

# OPTIONAL: verify Telegram’s secret header (set it when you call setWebhook)
SECRET = os.environ.get("TELEGRAM_WEBHOOK_SECRET", "")

# >>> THIS is what Vercel looks for <<<
app = Starlette()

@app.route("/", methods=["GET"])
async def health(_: Request):
    return PlainTextResponse("ok")

@app.route("/", methods=["POST"])
async def webhook(req: Request):
    # Header names are case-insensitive
    if SECRET:
        header = req.headers.get("x-telegram-bot-api-secret-token")
        if header != SECRET:
            return PlainTextResponse("forbidden", status_code=403)

    data = await req.json()
    update = Update.de_json(data, ptb.bot)
    await ptb.process_update(update)
    return JSONResponse({"ok": True})
