# api/tg_webhook.py
import os
import logging
from starlette.applications import Starlette
from starlette.requests import Request
from starlette.responses import <PERSON><PERSON><PERSON>esponse, PlainTextResponse
from telegram import Update, BotCommand
from telegram.ext import Application as PTBApp
from telegram.ext import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CallbackQueryHandler, filters

from app.config import TELEGRAM_BOT_TOKEN
from app.db import Base, engine
from app.bot import (
    start, help_cmd, setlang, add_cmd, import_cmd,
    today, week, month, review_cmd,
    reset_cmd, set_counter_cmd, stats, remove_cmd,
    on_button, on_text
)

# ---- Logging (captured by Vercel) ----
logging.basicConfig(
    level=logging.INFO,
    format='{"level":"%(levelname)s","msg":"%(message)s","ts":"%(asctime)s"}'
)
logger = logging.getLogger("tg_webhook")
# -------------------------------------

def build_ptb():
    # Ensure tables exist (safe to call on warm starts)
    Base.metadata.create_all(bind=engine)

    app = PTBApp.builder().token(TELEGRAM_BOT_TOKEN).build()

    # Register the same handlers you use in polling
    app.add_handler(CommandHandler("start", start))
    app.add_handler(CommandHandler("help", help_cmd))
    app.add_handler(CommandHandler("setlang", setlang))
    app.add_handler(CommandHandler("add", add_cmd))
    app.add_handler(CommandHandler("import", import_cmd))
    app.add_handler(CommandHandler("today", today))
    app.add_handler(CommandHandler("week", week))
    app.add_handler(CommandHandler("month", month))
    app.add_handler(CommandHandler("review", review_cmd))
    app.add_handler(CommandHandler("reset", reset_cmd))
    app.add_handler(CommandHandler("set_counter", set_counter_cmd))
    app.add_handler(CommandHandler("remove", remove_cmd))
    app.add_handler(CommandHandler("stats", stats))
    app.add_handler(CallbackQueryHandler(on_button))
    app.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, on_text))
    return app

ptb = build_ptb()
SECRET = os.environ.get("TELEGRAM_WEBHOOK_SECRET", "")

# --- Initialize/Start once per cold start, Stop/Shutdown on teardown ---
_initialized = False

async def _startup():
    global _initialized
    if not _initialized:
        logger.info("ptb_startup initializing=true")
        await ptb.initialize()
        await ptb.start()
        # Optional: set slash commands so they show up in the client
        try:
            await ptb.bot.set_my_commands([
                BotCommand("start", "Start the bot"),
                BotCommand("help", "Show help"),
                BotCommand("setlang", "Set target language (e.g., /setlang ru)"),
                BotCommand("add", "Add a word or phrase"),
                BotCommand("import", "Bulk import newline-separated words"),
                BotCommand("today", "List today’s words"),
                BotCommand("week", "List this week’s words"),
                BotCommand("month", "List this month’s words"),
                BotCommand("review", "Start review (today|week|month)"),
                BotCommand("reset", "Reset a word’s counter to 0"),
                BotCommand("set_counter", "Set global mastery threshold"),
                BotCommand("remove", "Remove a word from your list"),
                BotCommand("stats", "Show your stats"),
            ])
        except Exception as e:
            logger.warning("set_my_commands failed: %s", e)
        _initialized = True
        logger.info("ptb_startup initialized=true")

async def _shutdown():
    global _initialized
    if _initialized:
        logger.info("ptb_shutdown stopping=true")
        try:
            await ptb.stop()
            await ptb.shutdown()
        finally:
            _initialized = False
            logger.info("ptb_shutdown done")

# Starlette app with lifecycle hooks
app = Starlette(on_startup=[_startup], on_shutdown=[_shutdown])

# ---- Routes ----
@app.route("/api/tg_webhook", methods=["GET"])
async def health(req: Request):
    logger.info('health_check method=%s path=%s', req.method, req.url.path)
    return PlainTextResponse("ok")

@app.route("/api/tg_webhook", methods=["POST"])
async def webhook(req: Request):
    try:
        if SECRET:
            incoming = req.headers.get("x-telegram-bot-api-secret-token")
            ok = (incoming == SECRET)
            logger.info('secret_check ok=%s has_header=%s', ok, bool(incoming))
            if not ok:
                return PlainTextResponse("forbidden", status_code=403)

        data = await req.json()
        logger.info('update_received keys=%s', list(data.keys()))

        update = Update.de_json(data, ptb.bot)
        await ptb.process_update(update)

        logger.info('update_processed ok=true')
        return JSONResponse({"ok": True})
    except Exception as e:
        logger.exception("webhook_error: %s", e)
        # Return 200 to avoid Telegram retry storms; switch to 500 if you want retries.
        return JSONResponse({"ok": False}, status_code=200)

# Catch-all GET for debugging (also prints file path)
@app.route("/{rest_of_path:path}", methods=["GET"])
async def health_catchall(req: Request):
    current_file = os.path.abspath(__file__)
    logger.info('health_catchall method=%s path=%s file=%s', req.method, req.url.path, current_file)
    return PlainTextResponse(f"ok (backup)")

# CRITICAL: Expose the ASGI app for Vercel's Python runtime
# Vercel looks for 'app' variable at module level
application = app  # This is what Vercel will use
