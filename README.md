# Telegram Vocab <PERSON> (<PERSON><PERSON>, Python)

A minimal Telegram bot to save English words, fetch translations + usage examples (Reverso Context), and run a simple **know / don't know** review game. Ships with Docker and SQLite for local use.

## Features (MVP)
- Send a word or phrase → bot stores it, fetches translations + examples.
- Lists: `/today`, `/week`, `/month`.
- Review game: `/review today|week|month` with two buttons: **✅ Know** / **❌ Don't know**.
- After **5 correct** marks, a word becomes **known** and is skipped by default.
- Per-user target language via `/setlang <code>` (e.g., `ru`, `uk`, `es`).

## Requirements
- A Telegram bot token from [@BotFather](https://t.me/BotFather).
- Docker + docker-compose

## Quick Start
1. Copy `.env.example` → `.env`, set `TELEGRAM_BOT_TOKEN`, optional `DEFAULT_TARGET_LANG`.
2. Build + run:
   ```bash
   docker compose up --build
   ```
3. Talk to your bot on Telegram. Try:
   - `/start`
   - send a word: `serendipity`
   - `/today`
   - `/review today`

Data is saved in `./data/bot.db` (SQLite).

## Commands
- `/start` – register and help.
- `/setlang <code>` – set target language (e.g., `uk`, `ru`, `es`).
- `/add <word or phrase>` – add explicitly (or just send the text).
- `/today`, `/week`, `/month` – lists for date ranges.
- `/review [today|week|month]` – start a review session.
- `/stats` – your totals.
- `/help` – recap.

## Notes about Reverso & Google Translate
- This bot uses **`reverso-context-api`** (unofficial wrapper) to retrieve translations and usage examples from Reverso Context.
- It also uses **`googletrans`** (unofficial) and **`pygoogletranslation`** as a fallback to get a single quick translation.
- These libraries rely on web endpoints and may break or be rate-limited. For production-grade reliability, consider official/licensed APIs.

## Environment variables
- `TELEGRAM_BOT_TOKEN` (required)
- `DEFAULT_TARGET_LANG` (default: `ru`)
- `DB_URL` (default: `sqlite:////data/bot.db`)

## Project layout
```
app/
  bot.py                # Telegram handlers & game loop
  config.py             # env config
  db.py                 # SQLAlchemy engine/session
  models.py             # ORM models
  review.py             # review session manager
  utils.py              # helpers (date ranges, formatting)
  services/
    reverso_service.py  # Reverso Context translations + examples
    translate_service.py# Google Translate (unofficial) fallback
```

## License
MIT for this template. Check the licenses/ToS for Reverso/Google services separately.
# wordstreak
