{"version": 2, "builds": [{"src": "api/tg_webhook.py", "use": "@vercel/python", "config": {"maxLambdaSize": "50mb"}}, {"src": "api/tg_webhook_v2.py", "use": "@vercel/python", "config": {"maxLambdaSize": "50mb"}}, {"src": "api/tg_webhook_v3.py", "use": "@vercel/python", "config": {"maxLambdaSize": "50mb"}}, {"src": "api/tg_webhook_v4.py", "use": "@vercel/python", "config": {"maxLambdaSize": "50mb"}}], "routes": [{"src": "/api/tg_webhook", "dest": "/api/tg_webhook.py"}, {"src": "/api/tg_webhook_v2", "dest": "/api/tg_webhook_v2.py"}, {"src": "/api/tg_webhook_v3", "dest": "/api/tg_webhook_v3.py"}, {"src": "/api/tg_webhook_v4", "dest": "/api/tg_webhook_v4.py"}, {"src": "/(.*)", "dest": "/api/tg_webhook_v4.py"}], "env": {"PYTHONPATH": "."}}