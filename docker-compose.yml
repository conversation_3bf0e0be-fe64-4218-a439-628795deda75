version: "3.9"

services:
  db:
    image: postgres:16-alpine
    container_name: vocab-postgres
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-vocab_db}
      - POSTGRES_USER=${POSTGRES_USER:-vocab_user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-vocab_pass}
    volumes:
      - pgdata:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U $$POSTGRES_USER -d $$POSTGRES_DB"]
      interval: 5s
      timeout: 3s
      retries: 10
    ports:
      - "5432:5432"   # optional: expose to host tools (psql/GUI)

  bot:
    build: .
    container_name: telegram-vocab-bot
    env_file: .env
    environment:
      - DB_URL=${DB_URL:-postgresql+psycopg://vocab_user:vocab_pass@db:5432/vocab_db}
    volumes:
      - ./app:/app/app         # mount source code so edits don't need rebuild
      - ./api:/app/api         # mount webhook API code
      - ./run_webhook.py:/app/run_webhook.py  # mount webhook runner
    ports:
      - "8000:8000"           # expose webhook API for local testing
    depends_on:
      db:
        condition: service_healthy
    restart: unless-stopped
    command: ["python", "run_webhook.py"]  # run webhook server instead of polling bot

volumes:
  pgdata:
