import os

TELEGRAM_BOT_TOKEN = os.environ.get("TELEGRAM_BOT_TOKEN", "").strip()
DEFAULT_TARGET_LANG = os.environ.get("DEFAULT_TARGET_LANG", "ru").strip().lower()
DB_URL = os.environ.get("DB_URL", "sqlite:////data/bot.db")
ADMIN_USER_IDS = os.environ.get("ADMIN_USER_IDS", "")

DB_URL = os.environ.get(
    "DB_URL",
    "postgresql+psycopg://vocab_user:vocab_pass@db:5432/vocab_db"
)


if not TELEGRAM_BOT_TOKEN:
    raise RuntimeError("TELEGRAM_BOT_TOKEN is required (set it in environment or .env).")
