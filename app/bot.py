import asyncio
from datetime import datetime
from typing import List, Optional

from telegram import Update, InlineKeyboardMarkup, InlineKeyboardButton, BotCommand
from telegram.constants import ParseMode
from telegram.ext import (
    <PERSON>, CommandHandler, MessageHandler, CallbackQueryHandler,
    filters, ContextTypes
)

from sqlalchemy import select, func, and_
from sqlalchemy.orm import Session

from .config import TELEGRAM_BOT_TOKEN, DEFAULT_TARGET_LANG
from .db import Base, engine, SessionLocal
from .models import User, Word, Translation, Example, Review, WordStatus, BotConfig
from .services.reverso_service import fetch_from_reverso
from .services.translate_service import google_translate
from .utils import date_range_for, render_word_card, escape_html
from .review import ReviewManager

review_mgr = ReviewManager()


# ----- DB helpers -----
def init_db():
    Base.metadata.create_all(bind=engine)


def get_or_create_user(db: Session, telegram_id: int) -> User:
    user = db.execute(select(User).where(User.telegram_id == telegram_id)).scalar_one_or_none()
    if user is None:
        user = User(telegram_id=telegram_id, target_lang=DEFAULT_TARGET_LANG)
        db.add(user)
        db.commit()
        db.refresh(user)
    return user


def find_or_create_word(db: Session, user: User, text_en: str) -> Word:
    text_en_norm = text_en.strip().lower()
    word = db.execute(
        select(Word).where(and_(Word.user_id == user.id, Word.text_en == text_en_norm))
    ).scalar_one_or_none()
    if word is None:
        word = Word(user_id=user.id, text_en=text_en_norm, status=WordStatus.NEW)
        db.add(word)
        db.commit()
        db.refresh(word)
    return word


def save_translations(db: Session, word: Word, translations: List[str], source: str):
    # De-dup existing
    existing = {t.text_target for t in word.translations if t.source == source}
    for idx, t in enumerate(translations):
        if t and t not in existing:
            db.add(Translation(word_id=word.id, text_target=t, source=source, rank=idx))
    db.commit()


def save_examples(db: Session, word: Word, examples: List[tuple]):
    # examples: list of (src_en, tgt)
    existing = {(e.src_sentence, e.tgt_sentence) for e in word.examples}
    for (src, tgt) in examples:
        if (src, tgt) not in existing:
            db.add(Example(word_id=word.id, src_sentence=src, tgt_sentence=tgt, source_url=None))
    db.commit()


# ----- Mastery threshold helpers -----
DEFAULT_THRESHOLD = 5
CFG_KEY_THRESHOLD = "mastery_threshold"


def get_mastery_threshold(db: Session) -> int:
    cfg = db.execute(select(BotConfig).where(BotConfig.key == CFG_KEY_THRESHOLD)).scalar_one_or_none()
    if cfg and cfg.int_value:
        return int(cfg.int_value)
    return DEFAULT_THRESHOLD


def set_mastery_threshold(db: Session, value: int) -> None:
    cfg = db.execute(select(BotConfig).where(BotConfig.key == CFG_KEY_THRESHOLD)).scalar_one_or_none()
    if cfg is None:
        cfg = BotConfig(key=CFG_KEY_THRESHOLD, int_value=value)
        db.add(cfg)
    else:
        cfg.int_value = value
    db.commit()


async def handle_add_word(update: Update, context: ContextTypes.DEFAULT_TYPE, text: str):
    if not text:
        return
    with SessionLocal() as db:
        user = get_or_create_user(db, update.effective_user.id)
        word = find_or_create_word(db, user, text)

        # Fetch Reverso translations & examples
        trans, exs = fetch_from_reverso(
            word.text_en, source_lang="en", target_lang=user.target_lang,
            max_translations=5, max_examples=5
        )
        save_translations(db, word, trans, source="reverso")
        save_examples(db, word, exs)

        # Single Google translation as fallback or first suggestion
        gtr = google_translate(word.text_en, target_lang=user.target_lang, source_lang="en")
        if gtr:
            save_translations(db, word, [gtr], source="google")

        # Reload full objects
        db.refresh(word)
        translations = db.execute(
            select(Translation).where(Translation.word_id == word.id).order_by(Translation.source, Translation.rank)
        ).scalars().all()
        examples = db.execute(
            select(Example).where(Example.word_id == word.id).limit(5)
        ).scalars().all()

        threshold = get_mastery_threshold(db)
        reverso_url = f"https://context.reverso.net/translation/english-{user.target_lang}/{word.text_en}"
        await update.effective_chat.send_message(
            text=render_word_card(word, translations, examples, reverso_url, threshold=threshold),
            parse_mode=ParseMode.HTML,
            disable_web_page_preview=True
        )


# ----- Handlers -----
async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    with SessionLocal() as db:
        user = get_or_create_user(db, update.effective_user.id)
    msg = (
        "Hi! Send me an English word or phrase and I'll save it, fetch translations and examples.\n\n"
        "Lists: /today /week /month\n"
        "Review: /review today|week|month\n"
        "Bulk import: /import (paste newline-separated words)\n"
        "Remove a word: /remove <word>\n"
        f"Your target language: <b>{user.target_lang}</b> (change with /setlang &lt;code&gt;)"
    )
    await update.message.reply_html(msg)


async def help_cmd(update: Update, context: ContextTypes.DEFAULT_TYPE):
    await start(update, context)


async def setlang(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if not context.args:
        return await update.message.reply_text("Usage: /setlang <code>  e.g., ru, uk, es")
    code = context.args[0].lower().strip()
    with SessionLocal() as db:
        user = get_or_create_user(db, update.effective_user.id)
        user.target_lang = code
        db.commit()
    await update.message.reply_text(f"Target language set to: {code}")


# (/add remains available but is optional—plain text also adds a word)
async def add_cmd(update: Update, context: ContextTypes.DEFAULT_TYPE):
    text = " ".join(context.args).strip()
    if not text:
        return await update.message.reply_text("Usage: /add <word or phrase>")
    await handle_add_word(update, context, text)


async def on_text(update: Update, context: ContextTypes.DEFAULT_TYPE):
    # Any non-command text message will be treated as a word/phrase to add
    if update.message and update.message.text and not update.message.text.startswith('/'):
        await handle_add_word(update, context, update.message.text.strip())


def _list_words(db: Session, user_id: int, period: str) -> List[Word]:
    start, end = date_range_for(period)
    q = select(Word).where(
        and_(Word.user_id == user_id, Word.added_at >= start, Word.added_at <= end)
    ).order_by(Word.added_at.desc())
    return list(db.execute(q).scalars().all())


async def list_cmd(period: str, update: Update, context: ContextTypes.DEFAULT_TYPE):
    with SessionLocal() as db:
        user = get_or_create_user(db, update.effective_user.id)
        words = _list_words(db, user.id, period)
        threshold = get_mastery_threshold(db)
    if not words:
        return await update.message.reply_text(f"No words for {period}.")
    text = "\n".join(f"• {w.text_en} ({w.status.value}, {w.correct_count}/{threshold})" for w in words[:100])
    kb = InlineKeyboardMarkup([[InlineKeyboardButton(f"Start review: {period}", callback_data=f"review:{period}")]])
    await update.message.reply_text(text, reply_markup=kb)


async def today(update: Update, context: ContextTypes.DEFAULT_TYPE):
    await list_cmd("today", update, context)


async def week(update: Update, context: ContextTypes.DEFAULT_TYPE):
    await list_cmd("week", update, context)


async def month(update: Update, context: ContextTypes.DEFAULT_TYPE):
    await list_cmd("month", update, context)


# ----- Review -----
async def review_cmd(update: Update, context: ContextTypes.DEFAULT_TYPE):
    period = (context.args[0].lower() if context.args else "today")
    uid = update.effective_user.id
    with SessionLocal() as db:
        user = get_or_create_user(db, uid)
        words = _list_words(db, user.id, period)
        # exclude known by default
        word_ids = [w.id for w in words if w.status != WordStatus.KNOWN]
        threshold = get_mastery_threshold(db)
    if not word_ids:
        return await update.message.reply_text(f"No words to review for {period}.")
    review_mgr.start(uid, word_ids)
    await send_next_review_card(update, context, uid, threshold)


async def send_next_review_card(update: Update, context: ContextTypes.DEFAULT_TYPE, uid: int, threshold: Optional[int] = None):
    session = review_mgr.get(uid)
    if not session:
        return await context.bot.send_message(chat_id=update.effective_chat.id, text="No active review. Use /review.")
    wid = session.next_word_id()
    if wid is None:
        review_mgr.clear(uid)
        return await context.bot.send_message(chat_id=update.effective_chat.id, text="🎉 Review finished!")

    with SessionLocal() as db:
        if threshold is None:
            threshold = get_mastery_threshold(db)
        word = db.get(Word, wid)
        text = f"<b>{escape_html(word.text_en)}</b>   ({word.correct_count}/{threshold})"

    kb = InlineKeyboardMarkup([
        [
            InlineKeyboardButton("✅ Know", callback_data=f"know:{wid}"),
            InlineKeyboardButton("❌ Don't know", callback_data=f"dont:{wid}"),
            InlineKeyboardButton("📇 Show card", callback_data=f"card:{wid}"),
        ]
    ])
    await context.bot.send_message(
        chat_id=update.effective_chat.id,
        text=text,
        reply_markup=kb,
        parse_mode=ParseMode.HTML,
        disable_web_page_preview=True
    )


async def on_button(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    await query.answer()
    data = query.data or ""
    if data.startswith("review:"):
        # Kick off review from inline button under list
        period = data.split(":", 1)[1]
        query.data = None
        update.effective_message.text = f"/review {period}"
        return await review_cmd(update, context)

    if data.startswith("card:"):
        wid = int(data.split(":")[1])
        with SessionLocal() as db:
            word = db.get(Word, wid)
            translations = db.execute(
                select(Translation).where(Translation.word_id == wid).order_by(Translation.source, Translation.rank)
            ).scalars().all()
            examples = db.execute(select(Example).where(Example.word_id == wid).limit(5)).scalars().all()
            threshold = get_mastery_threshold(db)
            text = render_word_card(
                word,
                translations,
                examples,
                reverso_url=f"https://context.reverso.net/translation/english-{word.user.target_lang}/{word.text_en}",
                threshold=threshold
            )
        return await context.bot.send_message(
            chat_id=update.effective_chat.id,
            text=text,
            parse_mode=ParseMode.HTML,
            disable_web_page_preview=True
        )

    if data.startswith("know:") or data.startswith("dont:"):
        wid = int(data.split(":")[1])
        correct = data.startswith("know:")
        uid = update.effective_user.id
        with SessionLocal() as db:
            word = db.get(Word, wid)
            db.add(Review(word_id=wid, user_id=uid, correct=correct))
            # Update counters + status
            if correct:
                word.correct_count += 1
                threshold = get_mastery_threshold(db)
                if word.correct_count >= threshold:
                    word.status = WordStatus.KNOWN
            else:
                word.wrong_count += 1
            word.last_seen_at = datetime.utcnow()
            db.commit()
            threshold = get_mastery_threshold(db)
        # Send next
        await send_next_review_card(update, context, uid, threshold)


async def stats(update: Update, context: ContextTypes.DEFAULT_TYPE):
    uid = update.effective_user.id
    with SessionLocal() as db:
        user = get_or_create_user(db, uid)
        total = db.scalar(select(func.count(Word.id)).where(Word.user_id == user.id)) or 0
        known = db.scalar(
            select(func.count(Word.id)).where(and_(Word.user_id == user.id, Word.status == WordStatus.KNOWN))
        ) or 0
        reviews = db.scalar(select(func.count(Review.id)).where(Review.user_id == user.id)) or 0
        threshold = get_mastery_threshold(db)
    await update.message.reply_text(f"Words: {total} | Known: {known} | Reviews logged: {reviews} | Threshold: {threshold}")


# ----- New commands: reset, set_counter, import, remove -----
async def reset_cmd(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Reset the correct counter for a word to 0.
    Usage: /reset <word or phrase>
    """
    if not context.args:
        return await update.message.reply_text("Usage: /reset <word or phrase>")
    text = " ".join(context.args).strip().lower()
    uid = update.effective_user.id
    with SessionLocal() as db:
        user = get_or_create_user(db, uid)
        word = db.execute(select(Word).where(and_(Word.user_id == user.id, Word.text_en == text))).scalar_one_or_none()
        if not word:
            return await update.message.reply_text(f"Word not found: {text}")
        word.correct_count = 0
        db.commit()
        threshold = get_mastery_threshold(db)
    await update.message.reply_text(f"Counter reset: {text} (0/{threshold})")


async def set_counter_cmd(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Set the global mastery threshold (default 5).
    Usage: /set_counter <number between 1 and 20>
    """
    if not context.args:
        return await update.message.reply_text("Usage: /set_counter <number between 1 and 20>")
    try:
        value = int(context.args[0])
    except ValueError:
        return await update.message.reply_text("Please provide a valid integer (1–20).")
    if not (1 <= value <= 20):
        return await update.message.reply_text("Threshold must be between 1 and 20.")
    with SessionLocal() as db:
        set_mastery_threshold(db, value)
    await update.message.reply_text(f"Mastery threshold set to: {value}")


async def import_cmd(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Bulk import newline-separated words.
    Usage options:
      1) /import\nword one\nword two
      2) Reply to a message containing the list with /import
      3) /import word1\nword2 (Telegram may collapse newlines if typed inline)
    """
    payload = ""

    # Prefer text after command on the same message (after first newline or first space)
    if update.message and update.message.text:
        full = update.message.text
        if "\n" in full:
            payload = full.split("\n", 1)[1]
        else:
            parts = full.split(maxsplit=1)
            if len(parts) == 2:
                payload = parts[1]

    # If user replied to a message with /import, use the replied text
    if not payload and update.message and update.message.reply_to_message and update.message.reply_to_message.text:
        payload = update.message.reply_to_message.text

    if not payload:
        return await update.message.reply_text(
            "Paste newline-separated words after /import, or reply to a message with the list and send /import."
        )

    # Split by lines, normalize, and dedupe within this batch
    raw_words = [w.strip() for w in payload.splitlines()]
    words = []
    seen = set()
    for w in raw_words:
        if not w:
            continue
        lw = w.lower()
        if lw not in seen:
            seen.add(lw)
            words.append(lw)

    if not words:
        return await update.message.reply_text("No words detected. Please provide newline-separated words.")

    MAX_BATCH = 300
    truncated = False
    if len(words) > MAX_BATCH:
        words = words[:MAX_BATCH]
        truncated = True

    added, existing, failed = 0, 0, 0

    with SessionLocal() as db:
        user = get_or_create_user(db, update.effective_user.id)

        for w in words:
            existing_word = db.execute(
                select(Word).where(and_(Word.user_id == user.id, Word.text_en == w))
            ).scalar_one_or_none()
            if existing_word:
                existing += 1
                continue

            word = Word(user_id=user.id, text_en=w, status=WordStatus.NEW)
            db.add(word)
            db.commit()
            db.refresh(word)

            try:
                trans, exs = fetch_from_reverso(w, source_lang="en", target_lang=user.target_lang,
                                                max_translations=5, max_examples=5)
                save_translations(db, word, trans, source="reverso")
                save_examples(db, word, exs)

                gtr = google_translate(w, target_lang=user.target_lang, source_lang="en")
                if gtr:
                    save_translations(db, word, [gtr], source="google")

                added += 1
            except Exception:
                failed += 1

    summary = [f"✅ Imported: {added}", f"📦 Existing: {existing}"]
    if failed:
        summary.append(f"⚠️ Failed: {failed}")
    if truncated:
        summary.append(f"(Only first {MAX_BATCH} processed)")

    await update.message.reply_text("\n".join(summary))


async def remove_cmd(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Remove a word (and its translations/examples) from your vocabulary.
    Usage: /remove <word or phrase>
    """
    if not context.args:
        return await update.message.reply_text("Usage: /remove <word or phrase>")
    text = " ".join(context.args).strip().lower()
    uid = update.effective_user.id
    with SessionLocal() as db:
        user = get_or_create_user(db, uid)
        word = db.execute(
            select(Word).where(and_(Word.user_id == user.id, Word.text_en == text))
        ).scalar_one_or_none()
        if not word:
            return await update.message.reply_text(f"Word not found: {text}")
        # Cascades will delete translations/examples
        db.delete(word)
        db.commit()
    await update.message.reply_text(f"🗑️ Removed: {text}")


# ----- Command auto-suggestions (slash menu) -----
async def _set_commands(app: Application):
    await app.bot.set_my_commands([
        BotCommand("start", "Start the bot"),
        BotCommand("help", "Show help"),
        BotCommand("setlang", "Set target language (e.g., /setlang ru)"),
        BotCommand("add", "Add a word or phrase (optional—plain text also adds)"),
        BotCommand("import", "Bulk import newline-separated words"),
        BotCommand("today", "List today’s words"),
        BotCommand("week", "List this week’s words"),
        BotCommand("month", "List this month’s words"),
        BotCommand("review", "Start review (today|week|month)"),
        BotCommand("reset", "Reset a word’s counter to 0"),
        BotCommand("set_counter", "Set global mastery threshold"),
        BotCommand("remove", "Remove a word from your list"),
        BotCommand("stats", "Show your stats"),
    ])


def main():
    init_db()
    app = Application.builder() \
        .token(TELEGRAM_BOT_TOKEN) \
        .post_init(_set_commands) \
        .build()

    app.add_handler(CommandHandler("start", start))
    app.add_handler(CommandHandler("help", help_cmd))
    app.add_handler(CommandHandler("setlang", setlang))
    app.add_handler(CommandHandler("add", add_cmd))
    app.add_handler(CommandHandler("import", import_cmd))
    app.add_handler(CommandHandler("today", today))
    app.add_handler(CommandHandler("week", week))
    app.add_handler(CommandHandler("month", month))
    app.add_handler(CommandHandler("review", review_cmd))
    app.add_handler(CommandHandler("reset", reset_cmd))
    app.add_handler(CommandHandler("set_counter", set_counter_cmd))
    app.add_handler(CommandHandler("remove", remove_cmd))
    app.add_handler(CommandHandler("stats", stats))

    app.add_handler(CallbackQueryHandler(on_button))
    # Any non-command message becomes a new word
    app.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, on_text))

    app.run_polling(close_loop=False)


if __name__ == "__main__":
    main()
