from datetime import datetime, timed<PERSON>ta
from typing import <PERSON><PERSON>
from html import escape as _escape


def date_range_for(period: str) -> Tu<PERSON>[datetime, datetime]:
    now = datetime.utcnow()
    if period == "today":
        start = datetime(now.year, now.month, now.day)
        end = now
    elif period == "week":
        # last 7 days
        start = now - timedelta(days=7)
        end = now
    elif period == "month":
        start = now - timedelta(days=30)
        end = now
    else:
        # default to 7 days
        start = now - timedelta(days=7)
        end = now
    return start, end


def escape_html(s: str) -> str:
    """Escape HTML while leaving quotes unescaped for nicer typography."""
    return _escape(s, quote=False)


def render_word_card(word, translations, examples, reverso_url=None, threshold: int = 5) -> str:
    """
    Pretty HTML card:
      - title with the word
      - quick status + mastery counter
      - numbered translations
      - well-formatted examples
    """
    lines = [
        f"<b>🔤 {escape_html(word.text_en)}</b>",
        f"<i>Status:</i> {word.status.value}   <i>Mastery:</i> {word.correct_count}/{threshold}",
    ]
    if translations:
        lines.append("\n<b>🗣️ Translations</b>")
        for i, t in enumerate(translations[:5], start=1):
            lines.append(f"{i}. {escape_html(t.text_target)}")
    if examples:
        lines.append("\n<b>📚 Examples</b>")
        for ex in examples[:3]:
            lines.append(
                f"• “{escape_html(ex.src_sentence)}”\n"
                f"  <i>→ {escape_html(ex.tgt_sentence)}</i>"
                "\n"
            )
    if reverso_url:
        lines.append(f"\n<a href='{reverso_url}'>Open in Reverso Context</a>")
    return "\n".join(lines)
