from itertools import islice
from typing import List, <PERSON><PERSON>

# This package is an unofficial wrapper; may break over time.
try:
    from reverso_context_api import Client as ReversoClient
except Exception:
    ReversoClient = None

def fetch_from_reverso(word: str, source_lang: str, target_lang: str,
                       max_translations: int = 5, max_examples: int = 5):
    """Return (translations, examples) where:
       translations: List[str]
       examples: List[Tuple[str, str]]  # (en, target)
    """
    if not ReversoClient:
        return [], []

    try:
        client = ReversoClient(source_lang=source_lang, target_lang=target_lang)
        translations = list(islice(client.get_translations(word), max_translations))
        examples = list(islice(client.get_translation_samples(word), max_examples))
        return translations, examples
    except Exception:
        # Library or site may block/limit
        return [], []
