from typing import Optional

def google_translate(text: str, target_lang: str, source_lang: str = "en") -> Optional[str]:
    """Try googletrans first, then pygoogletranslation as fallback. Unofficial APIs."""
    # Try googletrans
    try:
        from googletrans import Translator as GT
        gt = GT()
        res = gt.translate(text, src=source_lang, dest=target_lang)
        if hasattr(res, "text"):
            return res.text
    except Exception:
        pass

    # Fallback: pygoogletranslation
    try:
        from pygoogletranslation import Translator as PGT
        tr = PGT()
        res = tr.translate(text, src=source_lang, dest=target_lang)
        if hasattr(res, "text"):
            return res.text
    except Exception:
        pass

    return None
