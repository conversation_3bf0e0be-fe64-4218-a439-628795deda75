import enum
from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Boolean, Enum, Text, UniqueConstraint
from sqlalchemy.orm import relationship

from .db import Base


class WordStatus(str, enum.Enum):
    NEW = "new"
    LEARNING = "learning"
    KNOWN = "known"
    SUSPENDED = "suspended"


class User(Base):
    __tablename__ = "users"
    id = Column(Integer, primary_key=True)
    telegram_id = Column(Integer, unique=True, index=True, nullable=False)
    target_lang = Column(String(10), default="ru", nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)

    words = relationship("Word", back_populates="user")


class Word(Base):
    __tablename__ = "words"
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True, nullable=False)
    text_en = Column(String(255), index=True, nullable=False)
    lemma_en = Column(String(255), nullable=True)
    status = Column(Enum(WordStatus), default=WordStatus.NEW, nullable=False)
    added_at = Column(DateTime, default=datetime.utcnow, index=True)
    correct_count = Column(Integer, default=0, nullable=False)
    wrong_count = Column(Integer, default=0, nullable=False)
    last_seen_at = Column(DateTime, nullable=True)

    user = relationship("User", back_populates="words")
    translations = relationship("Translation", back_populates="word", cascade="all, delete-orphan")
    examples = relationship("Example", back_populates="word", cascade="all, delete-orphan")

    __table_args__ = (UniqueConstraint("user_id", "text_en", name="uq_user_word"),)


class Translation(Base):
    __tablename__ = "translations"
    id = Column(Integer, primary_key=True)
    word_id = Column(Integer, ForeignKey("words.id"), index=True, nullable=False)
    text_target = Column(Text, nullable=False)
    source = Column(String(32), nullable=False)  # 'reverso' | 'google'
    rank = Column(Integer, default=0, nullable=False)

    word = relationship("Word", back_populates="translations")


class Example(Base):
    __tablename__ = "examples"
    id = Column(Integer, primary_key=True)
    word_id = Column(Integer, ForeignKey("words.id"), index=True, nullable=False)
    src_sentence = Column(Text, nullable=False)  # English
    tgt_sentence = Column(Text, nullable=False)  # Target language
    source_url = Column(Text, nullable=True)

    word = relationship("Word", back_populates="examples")


class Review(Base):
    __tablename__ = "reviews"
    id = Column(Integer, primary_key=True)
    word_id = Column(Integer, ForeignKey("words.id"), index=True, nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), index=True, nullable=False)
    ts = Column(DateTime, default=datetime.utcnow, index=True)
    correct = Column(Boolean, default=False, nullable=False)


class BotConfig(Base):
    """
    Simple key/value config store for global settings (e.g., mastery threshold).
    """
    __tablename__ = "bot_config"
    id = Column(Integer, primary_key=True)
    key = Column(String(64), unique=True, nullable=False, index=True)
    int_value = Column(Integer, nullable=True)
    str_value = Column(String(255), nullable=True)
