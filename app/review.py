import random
from dataclasses import dataclass, field
from typing import Dict, List, Optional

@dataclass
class ReviewSession:
    user_id: int
    word_ids: List[int]
    index: int = 0

    def next_word_id(self) -> Optional[int]:
        if self.index >= len(self.word_ids):
            return None
        wid = self.word_ids[self.index]
        self.index += 1
        return wid

class ReviewManager:
    def __init__(self):
        self.sessions: Dict[int, ReviewSession] = {}

    def start(self, user_id: int, word_ids: List[int]):
        random.shuffle(word_ids)
        self.sessions[user_id] = ReviewSession(user_id=user_id, word_ids=word_ids, index=0)

    def get(self, user_id: int) -> Optional[ReviewSession]:
        return self.sessions.get(user_id)

    def clear(self, user_id: int):
        if user_id in self.sessions:
            del self.sessions[user_id]
