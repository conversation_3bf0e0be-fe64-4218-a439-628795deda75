#!/usr/bin/env python3
"""
Test script to verify both webhook versions work locally before deploying to Vercel.
"""
import asyncio
import json

async def test_webhook_v1():
    """Test the original webhook version."""
    print("🧪 Testing Webhook V1 (api/tg_webhook.py)")
    try:
        from api.tg_webhook import app, application, _ensure_initialized
        print("✅ V1: App imported successfully")
        
        # Test initialization
        await _ensure_initialized()
        print("✅ V1: PTB app initialized successfully")
        
        print("✅ V1: All tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ V1 Error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_webhook_v2():
    """Test the new webhook version."""
    print("\n🧪 Testing Webhook V2 (api/tg_webhook_v2.py)")
    try:
        from api.tg_webhook_v2 import app, get_ptb_app, application
        print("✅ V2: App imported successfully")
        
        # Test that PTB app can be initialized
        ptb = await get_ptb_app()
        print("✅ V2: PTB app initialized successfully")
        
        # Test that the app is properly initialized
        if ptb.running:
            print("✅ V2: PTB app is running")
        else:
            print("⚠️  V2: PTB app created but not running")
        
        print("✅ V2: All tests passed!")
        
        # Clean up
        await ptb.stop()
        await ptb.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ V2 Error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Test both webhook versions."""
    print("🚀 Testing both webhook versions...\n")
    
    v1_success = await test_webhook_v1()
    v2_success = await test_webhook_v2()
    
    print("\n" + "="*50)
    print("📊 RESULTS:")
    print(f"V1 (Original): {'✅ PASS' if v1_success else '❌ FAIL'}")
    print(f"V2 (Serverless): {'✅ PASS' if v2_success else '❌ FAIL'}")
    
    if v1_success and v2_success:
        print("\n🎉 Both versions work! You can deploy and test both endpoints:")
        print("   • V1: https://your-app.vercel.app/api/tg_webhook")
        print("   • V2: https://your-app.vercel.app/api/tg_webhook_v2")
    elif v2_success:
        print("\n✅ V2 works! Use this endpoint for your Telegram webhook:")
        print("   • https://your-app.vercel.app/api/tg_webhook_v2")
    elif v1_success:
        print("\n✅ V1 works! Use this endpoint for your Telegram webhook:")
        print("   • https://your-app.vercel.app/api/tg_webhook")
    else:
        print("\n❌ Both versions failed. Check the errors above.")

if __name__ == "__main__":
    asyncio.run(main())
