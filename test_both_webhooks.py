#!/usr/bin/env python3
"""
Test script to verify all webhook versions work locally before deploying to Vercel.
"""
import asyncio
import json

async def test_webhook_v1():
    """Test the original webhook version."""
    print("🧪 Testing Webhook V1 (api/tg_webhook.py)")
    try:
        from api.tg_webhook import app, application, _ensure_initialized
        print("✅ V1: App imported successfully")

        # Test initialization
        await _ensure_initialized()
        print("✅ V1: PTB app initialized successfully")

        print("✅ V1: All tests passed!")
        return True

    except Exception as e:
        print(f"❌ V1 Error: {e}")
        return False

async def test_webhook_v2():
    """Test the serverless webhook version."""
    print("\n🧪 Testing Webhook V2 (api/tg_webhook_v2.py)")
    try:
        from api.tg_webhook_v2 import app, get_ptb_app, application
        print("✅ V2: App imported successfully")

        # Test that PTB app can be initialized
        ptb = await get_ptb_app()
        print("✅ V2: PTB app initialized successfully")

        print("✅ V2: All tests passed!")

        # Clean up
        try:
            await ptb.stop()
            await ptb.shutdown()
        except:
            pass  # Ignore cleanup errors
        return True

    except Exception as e:
        print(f"❌ V2 Error: {e}")
        return False

async def test_webhook_v3():
    """Test the event loop safe version."""
    print("\n🧪 Testing Webhook V3 (api/tg_webhook_v3.py)")
    try:
        from api.tg_webhook_v3 import app, get_bot_and_app, application
        print("✅ V3: App imported successfully")

        # Test that bot and PTB app can be initialized
        bot, ptb = await get_bot_and_app()
        print("✅ V3: Bot and PTB app initialized successfully")

        print("✅ V3: All tests passed!")

        # Clean up
        try:
            await ptb.stop()
            await ptb.shutdown()
        except:
            pass
        return True

    except Exception as e:
        print(f"❌ V3 Error: {e}")
        return False

async def test_webhook_v4():
    """Test the minimal serverless version."""
    print("\n🧪 Testing Webhook V4 (api/tg_webhook_v4.py)")
    try:
        from api.tg_webhook_v4 import app, get_ptb_app, application
        print("✅ V4: App imported successfully")

        # Test that PTB app can be initialized
        ptb, bot = await get_ptb_app()
        print("✅ V4: Minimal PTB app initialized successfully")

        print("✅ V4: All tests passed!")
        return True

    except Exception as e:
        print(f"❌ V4 Error: {e}")
        return False

async def main():
    """Test all webhook versions."""
    print("🚀 Testing all webhook versions...\n")

    v1_success = await test_webhook_v1()
    v2_success = await test_webhook_v2()
    v3_success = await test_webhook_v3()
    v4_success = await test_webhook_v4()

    print("\n" + "="*60)
    print("📊 RESULTS:")
    print(f"V1 (Original):     {'✅ PASS' if v1_success else '❌ FAIL'}")
    print(f"V2 (Serverless):   {'✅ PASS' if v2_success else '❌ FAIL'}")
    print(f"V3 (Event Safe):   {'✅ PASS' if v3_success else '❌ FAIL'}")
    print(f"V4 (Minimal):      {'✅ PASS' if v4_success else '❌ FAIL'}")

    working_versions = []
    if v1_success: working_versions.append("V1")
    if v2_success: working_versions.append("V2")
    if v3_success: working_versions.append("V3")
    if v4_success: working_versions.append("V4")

    if working_versions:
        print(f"\n🎉 {len(working_versions)} version(s) work! Available endpoints:")
        if v1_success: print("   • V1: https://your-app.vercel.app/api/tg_webhook")
        if v2_success: print("   • V2: https://your-app.vercel.app/api/tg_webhook_v2")
        if v3_success: print("   • V3: https://your-app.vercel.app/api/tg_webhook_v3")
        if v4_success: print("   • V4: https://your-app.vercel.app/api/tg_webhook_v4")

        print(f"\n💡 Recommendation: Try V4 first (minimal), then V3, then V2")
    else:
        print("\n❌ All versions failed. Check the errors above.")

if __name__ == "__main__":
    asyncio.run(main())
