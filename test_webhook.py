#!/usr/bin/env python3
"""
Test script to verify the webhook works locally before deploying to Vercel.
"""
import asyncio
import json

async def test_webhook():
    """Test that the webhook can be imported and initialized without errors."""
    try:
        # Test the new version
        from api.tg_webhook_v2 import app, get_ptb_app, application
        print("✅ App imported successfully")

        # Test that the ASGI app is exposed
        print("✅ ASGI application exposed correctly")

        # Test that PTB app can be initialized
        ptb = await get_ptb_app()
        print("✅ PTB app initialized successfully")

        # Test that the app is properly initialized
        if ptb.running:
            print("✅ PTB app is running")
        else:
            print("⚠️  PTB app created but not running")

        print("\n🎉 All tests passed! The webhook should work on Vercel.")

        # Clean up
        await ptb.stop()
        await ptb.shutdown()

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_webhook())
