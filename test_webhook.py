#!/usr/bin/env python3
"""
Test script to verify the webhook works locally before deploying to Vercel.
"""
import asyncio
import json
from api.tg_webhook import app

async def test_webhook():
    """Test that the webhook can be imported and initialized without errors."""
    try:
        # Test that the app can be imported
        print("✅ App imported successfully")
        
        # Test that the ASGI app is exposed
        from api.tg_webhook import application
        print("✅ ASGI application exposed correctly")
        
        # Test that PTB app can be initialized
        from api.tg_webhook import ptb
        print("✅ PTB app created successfully")
        
        print("\n🎉 All tests passed! The webhook should work on Vercel.")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_webhook())
