# --- Python ---
__pycache__/
*.py[cod]
*.pyo
*.pyd
*.so
*.egg-info/
.eggs/
dist/
build/
pip-wheel-metadata/
.pytest_cache/
.coverage
.coverage.*
.cache/
.mypy_cache/
.ruff_cache/
.pyre/
coverage.xml
htmlcov/
.python-version

# --- Virtual envs ---
.venv/
venv/

# --- OS / Editors ---
.DS_Store
Thumbs.db
.idea/
.vscode/
*.code-workspace
*.swp
*.swo
*.bak

# --- Environment / Secrets ---
.env
.env.*
!.env.example

# --- Project data (local files) ---
data/
pgdata/
*.db
*.sqlite
*.sqlite3

# --- Docker ---
*.log
docker-compose.override.yml

# --- Vercel / Deploy ---
.vercel/
.vercelignore

# --- Node (Vercel CLI optional) ---
node_modules/


to-do.txt